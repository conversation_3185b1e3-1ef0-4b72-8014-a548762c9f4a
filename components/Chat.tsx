"use client";

import { Send, Copy, ThumbsUp, ThumbsDown, User, Bot, ChevronDown, AlertCircle } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useSession } from "next-auth/react";
import { getOrCreateSession, type SessionData } from "@/lib/session";
import { contextService } from "@/lib/context";
import { ChatContext } from "@/lib/schemas";
import { useWebSocketChat } from "@/lib/use-websocket-chat";
import ReactMarkdown from "react-markdown";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { AutoTextarea } from "@/components/ui/auto-textarea";
import ContextChips from "./ContextChips";
import ContextOptions from "./ContextOptions";
import ContextOptionsButton from "./ContextOptionsButton";
import QuerySuggestions from "./QuerySuggestions";
import ConnectionStatus from "./ConnectionStatus";

export default function Chat() {
  const { data: authSession } = useSession();
  const [session, setSession] = useState<SessionData | null>(null);
  const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
  const [isUserScrolledUp, setIsUserScrolledUp] = useState(false);
  const [context, setContext] = useState<ChatContext>(() => contextService.getContext());
  const [isContextOptionsOpen, setIsContextOptionsOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Initialize session
  useEffect(() => {
    const sessionData = getOrCreateSession();
    setSession(sessionData);
  }, []);

  const { messages, input, handleInputChange, handleSubmit, isLoading, connectionStatus, error, retry } = useWebSocketChat({
    body: {
      userId: authSession?.user?.id || session?.userId,
      sessionId: session?.sessionId,
      userEmail: authSession?.user?.email || undefined,
    },
  });

  // Auto-scroll functionality
  const scrollToBottom = (smooth: boolean = true) => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'end'
      });
    }
  };

  const checkIfUserScrolledUp = () => {
    if (messagesContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px threshold
      setIsUserScrolledUp(!isAtBottom);
    }
  };

  // Auto-scroll when new messages arrive
  useEffect(() => {
    if (messages.length > 0 && !isUserScrolledUp) {
      scrollToBottom();
    }
  }, [messages, isUserScrolledUp]);

  // Auto-scroll when loading starts (new message being typed)
  useEffect(() => {
    if (isLoading && !isUserScrolledUp) {
      scrollToBottom();
    }
  }, [isLoading, isUserScrolledUp]);

  const copyToClipboard = async (text: string, messageId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 2000);
    } catch {
      // Silently fail if clipboard is not available
    }
  };

  // Context management functions
  const handleContextUpdate = (newContext: ChatContext) => {
    setContext(newContext);
  };

  const handleRemoveContextItem = (type: keyof ChatContext, itemId: string) => {
    const updatedContext = contextService.removeContextItem(type, itemId);
    setContext(updatedContext);
  };

  const handleOpenContextOptions = () => {
    setIsContextOptionsOpen(true);
  };

  const handleCloseContextOptions = () => {
    setIsContextOptionsOpen(false);
  };

  const handleSuggestionClick = (suggestion: string) => {
    // Set the suggestion as the input value
    handleInputChange({ target: { value: suggestion } } as React.ChangeEvent<HTMLTextAreaElement>);
  };

  return (
    <div className="flex flex-col flex-1 bg-background relative">
      {/* Connection Status Bar */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b border-border/40">
        <div className="max-w-4xl mx-auto px-4 py-2">
          <div className="flex items-center justify-between">
            <ConnectionStatus />
            {error && (
              <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                <AlertCircle size={16} />
                <span className="text-sm">{error}</span>
                <Button variant="ghost" size="sm" onClick={retry} className="h-6 px-2 text-xs">
                  Retry
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto max-w-4xl mx-auto w-full pr-4 sm:pr-6 lg:pr-8 max-h-[calc(100vh-180px)]"
        onScroll={checkIfUserScrolledUp}
      >
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center p-8 space-y-3">
            <div className="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
              <Bot size={32} className="text-white" />
            </div>

            <h3 className="text-lg font-semibold text-gray-800">
              You have reached the end of your chat history.
            </h3>

            <p className="text-gray-500">
              Hello! How can I assist you today?
            </p>
          </div>

        ) : (
          <div className="p-3 sm:p-4 space-y-4 sm:space-y-6">
            {messages.map((m, i) => (
              <div key={i} className={`flex gap-3 ${m.role === "user" ? "flex-row-reverse" : ""}`}>
                {/* Avatar */}
                <div className="flex-shrink-0">
                  {m.role === "user" ? (
                    authSession?.user?.image ? (
                      <Image
                        src={authSession.user.image}
                        alt={authSession.user.name || "User"}
                        width={32}
                        height={32}
                        className="rounded-full"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                        <User size={16} className="text-muted-foreground" />
                      </div>
                    )
                  ) : (
                    <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                      <Bot size={16} className="text-primary-foreground" />
                    </div>
                  )}
                </div>

                {/* Message Content */}
                <div className={`flex-1 min-w-0 ${m.role === "user" ? "text-right" : ""}`}>
                  <div className={`flex items-center gap-2 mb-2 ${m.role === "user" ? "justify-end" : ""}`}>
                    <span className="text-sm font-medium text-foreground">
                      {m.role === "user"
                        ? (authSession?.user?.name || "You")
                        : "AI Assistant"
                      }
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>

                  <div className={`max-w-none text-gray-700 dark:text-gray-300 ${m.role === "user" ? "text-right" : ""}`}>
                    {m.role === "user" ? (
                      <p className="text-base whitespace-pre-wrap">{m.content}</p>
                    ) : (
                      <div className="prose prose-base max-w-none dark:prose-invert">
                        <ReactMarkdown>{m.content}</ReactMarkdown>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons for AI messages */}
                  {m.role !== "user" && (
                    <div className="flex items-center gap-1 mt-3">
                      <button
                        onClick={() => copyToClipboard(m.content, `${i}`)}
                        className="flex items-center gap-1 px-2 py-1 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
                      >
                        <Copy size={12} />
                        {copiedMessageId === `${i}` ? "Copied!" : "Copy"}
                      </button>
                      <button className="flex items-center gap-1 px-2 py-1 text-sm text-gray-500 dark:text-gray-400 hover:text-green-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors">
                        <ThumbsUp size={12} />
                      </button>
                      <button className="flex items-center gap-1 px-2 py-1 text-sm text-gray-500 dark:text-gray-400 hover:text-red-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors">
                        <ThumbsDown size={12} />
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex gap-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <Bot size={16} className="text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">AI Assistant</span>
                  </div>
                  <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                    <span className="text-sm ml-2">AI is typing...</span>
                  </div>
                </div>
              </div>
            )}

            {/* Scroll anchor */}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Scroll to bottom button */}
      {isUserScrolledUp && (
        <div className="absolute bottom-20 right-8 z-10">
          <Button
            onClick={() => {
              scrollToBottom();
              setIsUserScrolledUp(false);
            }}
            className="rounded-full shadow-lg h-10 w-10"
            aria-label="Scroll to bottom"
          >
            <ChevronDown size={20} />
          </Button>
        </div>
      )}

      {/* Input Area */}
      <div className="p-3 sm:p-1 sticky bottom-0 bg-white"> 
        <div className="max-w-4xl mx-auto w-full space-y-3">
          {/* Query Suggestions */}
          <QuerySuggestions
            context={context}
            onSuggestionClick={handleSuggestionClick}
          />

          {/* Context Chips */}
          <ContextChips
            context={context}
            onRemoveItem={handleRemoveContextItem}
            className="px-1"
          />

          <form onSubmit={handleSubmit} className="relative">
            {/* Modern pill-shaped input container */}
            <div className="flex items-center bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg shadow-sm focus-within:ring-1 focus-within:ring-blue-500 focus-within:border-blue-500 transition-colors duration-200">
              {/* Context Options Button */}
              <div className="flex-shrink-0 pl-2">
                <ContextOptionsButton
                  onClick={handleOpenContextOptions}
                />
              </div>

              {/* Input field */}
              <div className="flex-1 relative">
                <AutoTextarea
                  className="w-full px-2 py-2 pr-10 bg-transparent text-md border-0 shadow-none focus-visible:ring-0 focus-visible:border-0 min-h-9"
                  value={input}
                  onChange={handleInputChange}
                  placeholder={connectionStatus === 'connected' ? "Message Finity..." : "Connecting to server..."}
                  disabled={connectionStatus !== 'connected' || isLoading}
                  minRows={1}
                  maxRows={12}
                  onKeyDown={(e: React.KeyboardEvent<HTMLTextAreaElement>) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      const form = e.currentTarget.form;
                      if (form) {
                        const formEvent = new Event("submit", { bubbles: true, cancelable: true });
                        handleSubmit(formEvent as unknown as React.FormEvent<HTMLFormElement>);
                      }
                    }
                  }}
                />
              </div>
              {/* Send button */}
              <div className="flex-shrink-0 pl-2 px-2 py-2">
                <Button
                  type="submit"
                  variant="ghost"
                  size="icon"
                  disabled={!input.trim() || isLoading || connectionStatus !== 'connected'}
                  className="h-10 w-10 text-muted-foreground hover:text-primary"
                >
                  <Send size={24} />
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>

      {/* Context Options Modal */}
      <ContextOptions
        isOpen={isContextOptionsOpen}
        onClose={handleCloseContextOptions}
        context={context}
        onContextUpdate={handleContextUpdate}
      />
    </div>
  );
}
