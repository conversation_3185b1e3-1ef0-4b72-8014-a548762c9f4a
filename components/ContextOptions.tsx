"use client";

import React from "react";
import { ChevronDown, Plus, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { ContextItem, ChatContext } from "@/lib/schemas";
import { contextService, getContextSummary } from "@/lib/context";

interface ContextOptionsProps {
  isOpen: boolean;
  onClose: () => void;
  context: ChatContext;
  onContextUpdate: (context: ChatContext) => void;
}

interface ContextSectionProps {
  title: string;
  type: keyof ChatContext;
  items: ContextItem[];
  availableOptions: ContextItem[];
  onAdd: (item: ContextItem) => void;
  onRemove: (itemId: string) => void;
  color: string;
}



function ContextSection({ title, items, availableOptions, onAdd, onRemove, color }: ContextSectionProps) {
  const selectedIds = new Set(items.map(item => item.id));
  const availableToAdd = availableOptions.filter(option => !selectedIds.has(option.id));

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">{title}</h4>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={availableToAdd.length === 0}
              className="h-7 px-2 text-xs"
            >
              <Plus size={12} />
              Add
              <ChevronDown size={12} />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-48 max-h-48" align="end">
            {availableToAdd.map((option) => (
              <DropdownMenuItem
                key={option.id}
                onClick={() => onAdd(option)}
                className="text-sm"
              >
                {option.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {items.length > 0 ? (
        <div className="space-y-2">
          {items.map((item) => (
            <div
              key={item.id}
              className={`flex items-center justify-between p-2 rounded-md ${color}`}
            >
              <span className="text-sm font-medium">{item.label}</span>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => onRemove(item.id)}
                className="h-6 w-6 hover:bg-black/10 dark:hover:bg-white/10"
                aria-label={`Remove ${item.label}`}
              >
                <Trash2 size={12} />
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-muted-foreground italic">No {title.toLowerCase()} selected</p>
      )}
    </div>
  );
}

export default function ContextOptions({ isOpen, onClose, context, onContextUpdate }: ContextOptionsProps) {

  const handleAddItem = (type: keyof ChatContext, item: ContextItem) => {
    const updatedContext = contextService.addContextItem(type, item);
    onContextUpdate(updatedContext);
  };

  const handleRemoveItem = (type: keyof ChatContext, itemId: string) => {
    const updatedContext = contextService.removeContextItem(type, itemId);
    onContextUpdate(updatedContext);
  };

  const handleClearAll = () => {
    const clearedContext = contextService.clearContext();
    onContextUpdate(clearedContext);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Context Settings</DialogTitle>
          <DialogDescription>
            {getContextSummary(context)}
          </DialogDescription>
        </DialogHeader>

        {/* Content */}
        <div className="space-y-6 overflow-y-auto max-h-[60vh]">
          <ContextSection
            title="Brands"
            type="brands"
            items={context.brands}
            availableOptions={contextService.getAvailableOptions('brands')}
            onAdd={(item) => handleAddItem('brands', item)}
            onRemove={(itemId) => handleRemoveItem('brands', itemId)}
            color="bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300"
          />

          <ContextSection
            title="Time Periods"
            type="timePeriods"
            items={context.timePeriods}
            availableOptions={contextService.getAvailableOptions('timePeriods')}
            onAdd={(item) => handleAddItem('timePeriods', item)}
            onRemove={(itemId) => handleRemoveItem('timePeriods', itemId)}
            color="bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300"
          />

          <ContextSection
            title="Metrics"
            type="metrics"
            items={context.metrics}
            availableOptions={contextService.getAvailableOptions('metrics')}
            onAdd={(item) => handleAddItem('metrics', item)}
            onRemove={(itemId) => handleRemoveItem('metrics', itemId)}
            color="bg-purple-50 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300"
          />
        </div>

        {/* Footer */}
        <div className="flex justify-between pt-4 border-t border-border">
          <Button
            variant="destructive"
            onClick={handleClearAll}
            className="text-sm"
          >
            Clear All
          </Button>
          <Button
            onClick={onClose}
            className="text-sm"
          >
            Done
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
