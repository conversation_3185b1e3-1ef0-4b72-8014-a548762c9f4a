"use client";

import { Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ContextOptionsButtonProps {
  onClick: () => void;
  className?: string;
}

export default function ContextOptionsButton({ onClick, className = "" }: ContextOptionsButtonProps) {
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={onClick}
      className={cn("h-10 w-10 text-muted-foreground hover:text-primary", className)}
      title="Context Settings"
      aria-label="Open context settings"
    >
      <Settings size={24} />
    </Button>
  );
}
