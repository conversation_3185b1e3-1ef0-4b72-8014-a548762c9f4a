import { NextAuthOptions } from "next-auth"
import GoogleProvider from "next-auth/providers/google"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    })
  ],
  session: {
    strategy: "database",
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  callbacks: {
    async session({ session, user }) {
      // Include user id and custom fields in session
      if (session.user) {
        session.user.id = user.id
        // Add custom fields from database
        const dbUser = await prisma.user.findUnique({
          where: { id: user.id },
          include: { company: true }
        })
        if (dbUser) {
          session.user.role = dbUser.role
          session.user.companyId = dbUser.companyId
          session.user.company = dbUser.company
        }
      }
      return session
    },
    async signIn({ account }) {
      // You can add custom logic here for sign-in validation
      // For example, restrict access to specific domains
      if (account?.provider === "google") {
        // Optional: Add domain restriction
        // const allowedDomains = ["yourcompany.com"]
        // if (user.email && !allowedDomains.some(domain => user.email!.endsWith(domain))) {
        //   return false
        // }
        return true
      }
      return true
    },
    async jwt({ token, account }) {
      // Persist the OAuth access_token to the token right after signin
      if (account) {
        token.accessToken = account.access_token
      }
      return token
    }
  },
  events: {
    async createUser() {
      // You can add custom logic here when a new user is created
      // For example, send welcome email, assign default role, etc.
    },
    async signIn() {
      // You can add custom logic here when a user signs in
      // For example, update last login time, track analytics, etc.
    }
  },
  debug: process.env.NODE_ENV === "development",
}
