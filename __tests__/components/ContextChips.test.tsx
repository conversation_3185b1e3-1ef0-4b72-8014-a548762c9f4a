import { render, screen, fireEvent } from '@testing-library/react'
import ContextChips from '@/components/ContextChips'
import { ChatContext } from '@/lib/schemas'

const mockContext: ChatContext = {
  brands: [
    { id: 'brand-1', label: 'Brand A', value: 'Brand A' },
    { id: 'brand-2', label: 'Brand B', value: 'Brand B' },
  ],
  timePeriods: [
    { id: 'q1-2024', label: 'Q1 2024', value: 'Q1 2024' },
  ],
  metrics: [
    { id: 'revenue', label: 'Revenue', value: 'Revenue' },
    { id: 'profit', label: 'Profit', value: 'Profit' },
  ],
  lastUpdated: Date.now(),
}

const mockOnRemoveItem = jest.fn()

describe('ContextChips Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders all context chips correctly', () => {
    render(
      <ContextChips 
        context={mockContext} 
        onRemoveItem={mockOnRemoveItem} 
      />
    )

    // Check brand chips
    expect(screen.getByText('Brand A')).toBeInTheDocument()
    expect(screen.getByText('Brand B')).toBeInTheDocument()

    // Check time period chips
    expect(screen.getByText('Q1 2024')).toBeInTheDocument()

    // Check metric chips
    expect(screen.getByText('Revenue')).toBeInTheDocument()
    expect(screen.getByText('Profit')).toBeInTheDocument()
  })

  it('applies correct variant classes for different chip types', () => {
    render(
      <ContextChips 
        context={mockContext} 
        onRemoveItem={mockOnRemoveItem} 
      />
    )

    const brandChip = screen.getByText('Brand A').closest('.inline-flex')
    const timePeriodChip = screen.getByText('Q1 2024').closest('.inline-flex')
    const metricChip = screen.getByText('Revenue').closest('.inline-flex')

    // Brand chips should have blue styling
    expect(brandChip).toHaveClass('bg-blue-100')
    
    // Time period chips should have green styling
    expect(timePeriodChip).toHaveClass('bg-green-100')
    
    // Metric chips should have purple styling
    expect(metricChip).toHaveClass('bg-purple-100')
  })

  it('calls onRemoveItem when remove button is clicked', () => {
    render(
      <ContextChips 
        context={mockContext} 
        onRemoveItem={mockOnRemoveItem} 
      />
    )

    // Find and click the remove button for Brand A
    const brandAChip = screen.getByText('Brand A').closest('.inline-flex')
    const removeButton = brandAChip?.querySelector('button')
    
    expect(removeButton).toBeInTheDocument()
    fireEvent.click(removeButton!)

    expect(mockOnRemoveItem).toHaveBeenCalledWith('brands', 'brand-1')
  })

  it('renders nothing when context is empty', () => {
    const emptyContext: ChatContext = {
      brands: [],
      timePeriods: [],
      metrics: [],
      lastUpdated: Date.now(),
    }

    const { container } = render(
      <ContextChips 
        context={emptyContext} 
        onRemoveItem={mockOnRemoveItem} 
      />
    )

    expect(container.firstChild).toBeNull()
  })

  it('applies custom className', () => {
    const { container } = render(
      <ContextChips 
        context={mockContext} 
        onRemoveItem={mockOnRemoveItem}
        className="custom-chips"
      />
    )

    expect(container.firstChild).toHaveClass('custom-chips')
  })

  it('has proper accessibility attributes', () => {
    render(
      <ContextChips 
        context={mockContext} 
        onRemoveItem={mockOnRemoveItem} 
      />
    )

    // Check that remove buttons have proper aria-labels
    const removeButtons = screen.getAllByRole('button')
    
    expect(removeButtons[0]).toHaveAttribute('aria-label', 'Remove Brand A')
    expect(removeButtons[1]).toHaveAttribute('aria-label', 'Remove Brand B')
    expect(removeButtons[2]).toHaveAttribute('aria-label', 'Remove Q1 2024')
  })

  it('truncates long labels correctly', () => {
    const longLabelContext: ChatContext = {
      brands: [
        { 
          id: 'long-brand', 
          label: 'This is a very long brand name that should be truncated', 
          value: 'Long Brand' 
        },
      ],
      timePeriods: [],
      metrics: [],
      lastUpdated: Date.now(),
    }

    render(
      <ContextChips 
        context={longLabelContext} 
        onRemoveItem={mockOnRemoveItem} 
      />
    )

    const labelSpan = screen.getByText('This is a very long brand name that should be truncated')
    expect(labelSpan).toHaveClass('truncate', 'max-w-24')
  })
})
