# Component Documentation

This document provides an overview of the UI components used in the Finity AI Chat application, built with shadcn/ui and Radix UI primitives.

## Overview

All UI components are built using [shadcn/ui](https://ui.shadcn.com/), which provides:

- **Accessibility**: WCAG compliant components with proper ARIA attributes
- **Customization**: Tailwind CSS-based styling with CSS variables
- **Type Safety**: Full TypeScript support with proper component typing
- **Consistency**: Unified design system across the application

## Core Components

### Button

The Button component provides various interactive button styles and behaviors.

**Variants:**
- `default`: Primary action button with solid background
- `destructive`: For dangerous actions (delete, remove)
- `outline`: Secondary actions with border styling
- `ghost`: Minimal styling for subtle actions
- `link`: Text-only button that looks like a link

**Sizes:**
- `default`: Standard button size (h-9)
- `sm`: Small button (h-8)
- `lg`: Large button (h-10)
- `icon`: Square button for icons (size-9)

**Usage:**
```tsx
import { But<PERSON> } from "@/components/ui/button"

<Button variant="default" size="sm">
  Click me
</Button>
```

### Badge

Status indicators and labels with semantic meaning.

**Variants:**
- `default`: Primary badge styling
- `secondary`: Muted badge styling
- `destructive`: Error or warning states
- `outline`: Bordered badge styling

**Custom Variants (ContextChips):**
- `brand`: Blue styling for brand context
- `timePeriod`: Green styling for time periods
- `metric`: Purple styling for metrics

**Usage:**
```tsx
import { Badge } from "@/components/ui/badge"

<Badge variant="outline">Status</Badge>
```

### Card

Container component for grouping related content.

**Sub-components:**
- `Card`: Main container
- `CardHeader`: Top section for titles
- `CardContent`: Main content area
- `CardFooter`: Bottom section for actions

**Usage:**
```tsx
import { Card, CardContent, CardHeader } from "@/components/ui/card"

<Card>
  <CardHeader>
    <h3>Title</h3>
  </CardHeader>
  <CardContent>
    <p>Content goes here</p>
  </CardContent>
</Card>
```

### Dialog

Modal dialogs for forms, confirmations, and detailed views.

**Sub-components:**
- `Dialog`: Root component
- `DialogTrigger`: Button that opens the dialog
- `DialogContent`: Main dialog container
- `DialogHeader`: Header section
- `DialogTitle`: Dialog title
- `DialogDescription`: Dialog description

**Usage:**
```tsx
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

<Dialog>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
    </DialogHeader>
    <p>Dialog content</p>
  </DialogContent>
</Dialog>
```

### Input & Textarea

Form input components with consistent styling and validation states.

**Features:**
- Consistent styling across all input types
- Support for validation states
- Proper focus and hover states
- Accessibility attributes

**Custom Components:**
- `AutoTextarea`: Combines shadcn/ui styling with react-textarea-autosize

**Usage:**
```tsx
import { Input } from "@/components/ui/input"
import { AutoTextarea } from "@/components/ui/auto-textarea"

<Input placeholder="Enter text..." />
<AutoTextarea placeholder="Auto-resizing textarea..." />
```

### DropdownMenu

Accessible dropdown menus for actions and selections.

**Sub-components:**
- `DropdownMenu`: Root component
- `DropdownMenuTrigger`: Button that opens the menu
- `DropdownMenuContent`: Menu container
- `DropdownMenuItem`: Individual menu items
- `DropdownMenuSeparator`: Visual separators

**Usage:**
```tsx
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"

<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button>Open Menu</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>Action 1</DropdownMenuItem>
    <DropdownMenuItem>Action 2</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

## Application-Specific Components

### ThemeToggle

Toggles between light and dark themes using next-themes.

**Features:**
- Automatic theme detection
- Smooth transitions
- Icon changes based on current theme
- Keyboard accessible

### ContextChips

Displays selected context items (brands, time periods, metrics) as removable badges.

**Features:**
- Color-coded by context type
- Remove functionality
- Truncated text for long labels
- Accessible remove buttons

### SessionInfo

Collapsible card showing current session information.

**Features:**
- Expandable/collapsible interface
- Session reset functionality
- Formatted session IDs
- Card-based layout

## Theming

The application uses CSS variables for consistent theming:

```css
:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  /* ... more variables */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  /* ... dark theme overrides */
}
```

## Testing

All components are thoroughly tested with Jest and React Testing Library:

- **Unit Tests**: Individual component behavior
- **Integration Tests**: Component interactions
- **Accessibility Tests**: ARIA attributes and keyboard navigation
- **Visual Tests**: Styling and variant rendering

Run tests with:
```bash
npm test              # Run all tests
npm run test:watch    # Watch mode
npm run test:coverage # Coverage report
```

## Best Practices

1. **Accessibility**: Always include proper ARIA labels and keyboard navigation
2. **TypeScript**: Use proper typing for component props and variants
3. **Styling**: Prefer design tokens over hardcoded colors
4. **Testing**: Write tests for all interactive behaviors
5. **Performance**: Use `asChild` prop for composition when needed

## Migration Notes

This application was migrated from custom components to shadcn/ui. Key benefits:

- **Consistency**: Unified design system
- **Accessibility**: Built-in WCAG compliance
- **Maintainability**: Reduced custom CSS and state management
- **Developer Experience**: Better TypeScript support and documentation
