import { type VariantProps } from "class-variance-authority";
import { buttonVariants } from "@/components/ui/button";
import { badgeVariants } from "@/components/ui/badge";

// Export variant types for better type safety across the application
export type ButtonVariant = VariantProps<typeof buttonVariants>["variant"];
export type ButtonSize = VariantProps<typeof buttonVariants>["size"];
export type BadgeVariant = VariantProps<typeof badgeVariants>["variant"];

// Common UI component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Extended component props for components that support variants
export interface ButtonProps extends BaseComponentProps {
  variant?: ButtonVariant;
  size?: ButtonSize;
  asChild?: boolean;
  disabled?: boolean;
}

export interface BadgeProps extends BaseComponentProps {
  variant?: BadgeVariant;
  asChild?: boolean;
}

// Dialog component types
export interface DialogProps extends BaseComponentProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

// Dropdown menu types
export interface DropdownMenuProps extends BaseComponentProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

// Card component types
export type CardProps = BaseComponentProps

// Input component types
export interface InputProps extends BaseComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}

// Textarea component types
export interface TextareaProps extends BaseComponentProps {
  placeholder?: string;
  value?: string;
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  disabled?: boolean;
  rows?: number;
}
