"use client";

import { useState, useEffect, useCallback } from "react";
import { Lightbulb, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { ChatContext } from "@/lib/schemas";
import { hasActiveContext } from "@/lib/context";

interface QuerySuggestionsProps {
  context: ChatContext;
  onSuggestionClick: (suggestion: string) => void;
  className?: string;
}

interface Suggestion {
  id: string;
  text: string;
  description: string;
}

export default function QuerySuggestions({ context, onSuggestionClick, className = "" }: QuerySuggestionsProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);

  const generateSuggestions = useCallback(() => {
    const newSuggestions: Suggestion[] = [];

    // Single context suggestions
    if (context.brands.length === 1 && context.timePeriods.length === 1) {
      const brand = context.brands[0];
      const period = context.timePeriods[0];
      newSuggestions.push({
        id: 'single-brand-period',
        text: `Show ${brand.label} performance in ${period.label}`,
        description: 'Analyze single brand in specific period'
      });
    }

    // Comparative suggestions
    if (context.brands.length >= 2) {
      const brand1 = context.brands[0];
      const brand2 = context.brands[1];
      newSuggestions.push({
        id: 'compare-brands',
        text: `Compare ${brand1.label} vs ${brand2.label}`,
        description: 'Compare performance between brands'
      });
    }

    if (context.timePeriods.length >= 2) {
      const period1 = context.timePeriods[0];
      const period2 = context.timePeriods[1];
      newSuggestions.push({
        id: 'compare-periods',
        text: `Compare ${period1.label} vs ${period2.label}`,
        description: 'Compare performance across time periods'
      });
    }

    // Multi-dimensional suggestions
    if (context.brands.length >= 1 && context.timePeriods.length >= 2 && context.metrics.length >= 1) {
      const brand = context.brands[0];
      const period1 = context.timePeriods[0];
      const period2 = context.timePeriods[1];
      const metric = context.metrics[0];
      newSuggestions.push({
        id: 'brand-trend',
        text: `How did ${brand.label} ${metric.label.toLowerCase()} change from ${period1.label} to ${period2.label}?`,
        description: 'Analyze brand trends over time'
      });
    }

    if (context.brands.length >= 2 && context.timePeriods.length >= 1 && context.metrics.length >= 1) {
      const brand1 = context.brands[0];
      const brand2 = context.brands[1];
      const period = context.timePeriods[0];
      const metric = context.metrics[0];
      newSuggestions.push({
        id: 'competitive-analysis',
        text: `Compare ${brand1.label} and ${brand2.label} ${metric.label.toLowerCase()} in ${period.label}`,
        description: 'Competitive analysis in specific period'
      });
    }

    // Metric-focused suggestions
    if (context.metrics.length >= 1) {
      const metric = context.metrics[0];
      if (context.brands.length >= 1) {
        const brand = context.brands[0];
        newSuggestions.push({
          id: 'metric-analysis',
          text: `Analyze ${brand.label} ${metric.label.toLowerCase()} performance`,
          description: 'Deep dive into specific metric'
        });
      }
    }

    // General suggestions when context is set
    if (hasActiveContext(context)) {
      newSuggestions.push({
        id: 'summary',
        text: 'Give me a summary of the selected context',
        description: 'Overview of all selected items'
      });

      newSuggestions.push({
        id: 'insights',
        text: 'What insights can you provide about this data?',
        description: 'AI-generated insights and recommendations'
      });
    }

    setSuggestions(newSuggestions.slice(0, 4)); // Limit to 4 suggestions
  }, [context]);

  useEffect(() => {
    if (hasActiveContext(context)) {
      generateSuggestions();
      setIsVisible(true);
    } else {
      setIsVisible(false);
    }
  }, [context, generateSuggestions]);

  if (!isVisible || suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3 ${className}`}>
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center gap-2">
          <Lightbulb size={14} className="text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
          <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
            Suggested queries
          </span>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setIsVisible(false)}
          className="h-6 w-6 hover:bg-primary/20"
          aria-label="Hide suggestions"
        >
          <X size={12} className="text-primary" />
        </Button>
      </div>

      <div className="space-y-2">
        {suggestions.map((suggestion) => (
          <Button
            key={suggestion.id}
            variant="outline"
            onClick={() => onSuggestionClick(suggestion.text)}
            className="w-full justify-start h-auto p-2 border-primary/20 hover:bg-primary/10 group"
          >
            <div className="text-left">
              <div className="text-sm font-medium group-hover:text-primary">
                {suggestion.text}
              </div>
              <div className="text-xs text-muted-foreground mt-0.5">
                {suggestion.description}
              </div>
            </div>
          </Button>
        ))}
      </div>

      <div className="mt-3 pt-2 border-t border-blue-200 dark:border-blue-700">
        <p className="text-xs text-blue-700 dark:text-blue-300">
          💡 Tip: You can ask comparative questions like &ldquo;Compare Brand A in July with Brand B in August&rdquo;
        </p>
      </div>
    </div>
  );
}
