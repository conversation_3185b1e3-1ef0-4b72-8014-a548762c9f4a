'use client';

import { WebSocketMessagePayload, Message } from './schemas';

export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

export interface WebSocketRequest {
  id: string;
  type: 'chat_message';
  payload: WebSocketMessagePayload;
  timestamp: number;
}

export interface WebSocketResponse {
  id: string;
  type: 'chat_response' | 'error';
  payload: string | { error: string };
  timestamp: number;
}

export interface ConnectionStatusListener {
  (status: ConnectionStatus): void;
}

export interface MessageListener {
  (response: WebSocketResponse): void;
}

class WebSocketClient {
  private ws: WebSocket | null = null;
  private url: string;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // Start with 1 second
  private maxReconnectDelay = 30000; // Max 30 seconds
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;

  private statusListeners: Set<ConnectionStatusListener> = new Set();
  private messageListeners: Set<MessageListener> = new Set();
  private pendingRequests: Map<string, {
    resolve: (response: WebSocketResponse) => void;
    reject: (error: Error) => void;
    timeout: NodeJS.Timeout;
  }> = new Map();

  private messageQueue: WebSocketRequest[] = [];

  constructor(url?: string) {
    this.url = url || process.env.NEXT_PUBLIC_WEBSOCKET_URL || 'ws://103.234.211.131:8880/ws';
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      this.setConnectionStatus('connecting');

      try {
        this.ws = new WebSocket(this.url);

        const connectTimeout = setTimeout(() => {
          this.ws?.close();
          reject(new Error('Connection timeout'));
        }, 5000);

        this.ws.onopen = () => {
          clearTimeout(connectTimeout);
          this.setConnectionStatus('connected');
          this.reconnectAttempts = 0;
          this.reconnectDelay = 1000;
          this.startHeartbeat();
          this.processMessageQueue();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          clearTimeout(connectTimeout);
          this.cleanup();

          if (event.code !== 1000) { // Not a normal closure
            this.setConnectionStatus('error');
            this.scheduleReconnect();
          } else {
            this.setConnectionStatus('disconnected');
          }
        };

        this.ws.onerror = () => {
          clearTimeout(connectTimeout);
          this.setConnectionStatus('error');
          reject(new Error('WebSocket connection failed'));
        };

      } catch (error) {
        this.setConnectionStatus('error');
        reject(error);
      }
    });
  }

  disconnect(): void {
    this.cleanup();
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    this.setConnectionStatus('disconnected');
  }

  sendMessage(messages: Message[]): Promise<WebSocketResponse> {
    return new Promise((resolve, reject) => {
      const request: WebSocketRequest = {
        id: this.generateRequestId(),
        type: 'chat_message',
        payload: { messages },
        timestamp: Date.now(),
      };

      // Set up response handling
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(request.id);
        reject(new Error('Request timeout'));
      }, 30000); // 30 second timeout

      this.pendingRequests.set(request.id, {
        resolve,
        reject,
        timeout,
      });

      if (this.connectionStatus === 'connected' && this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(request));
      } else {
        // Queue the message for when connection is restored
        this.messageQueue.push(request);

        // Try to connect if not already connecting
        if (this.connectionStatus === 'disconnected') {
          this.connect().catch(() => {
            // Connection failed, reject the request
            this.pendingRequests.delete(request.id);
            clearTimeout(timeout);
            reject(new Error('Failed to establish WebSocket connection'));
          });
        }
      }
    });
  }

  getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  onStatusChange(listener: ConnectionStatusListener): () => void {
    this.statusListeners.add(listener);
    return () => this.statusListeners.delete(listener);
  }

  onMessage(listener: MessageListener): () => void {
    this.messageListeners.add(listener);
    return () => this.messageListeners.delete(listener);
  }

  private setConnectionStatus(status: ConnectionStatus): void {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status;
      this.statusListeners.forEach(listener => listener(status));
    }
  }

  private handleMessage(data: string): void {
    try {
      const response: WebSocketResponse = JSON.parse(data);

      // Handle pending request
      const pendingRequest = this.pendingRequests.get(response.id);
      if (pendingRequest) {
        clearTimeout(pendingRequest.timeout);
        this.pendingRequests.delete(response.id);
        pendingRequest.resolve(response);
      }

      // Notify message listeners
      this.messageListeners.forEach(listener => listener(response));

    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        // Send a ping frame (browser WebSocket doesn't have ping method, so we send a custom message)
        try {
          this.ws.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
        } catch (error) {
          console.warn('Failed to send heartbeat:', error);
        }
      }
    }, 30000); // 30 seconds
  }

  private cleanup(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    // Reject all pending requests
    this.pendingRequests.forEach(({ reject, timeout }) => {
      clearTimeout(timeout);
      reject(new Error('Connection closed'));
    });
    this.pendingRequests.clear();
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);

    this.reconnectTimeout = setTimeout(() => {
      this.connect().catch(() => {
        // Reconnection failed, will try again
      });
    }, delay);
  }

  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.ws?.readyState === WebSocket.OPEN) {
      const request = this.messageQueue.shift();
      if (request) {
        this.ws.send(JSON.stringify(request));
      }
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

// Export singleton instance
export const webSocketClient = new WebSocketClient();
