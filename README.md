# Finity AI Chat

A modern, production-ready AI chat application built with Next.js, featuring WebSocket-based real-time communication, advanced context management, and secure authentication.

## 🚀 Features

- **Real-time WebSocket Communication**: Direct WebSocket connection to fin_gateway for instant messaging
- **Connection Status Monitoring**: Real-time connection status indicators with automatic reconnection
- **AI-Powered Chat**: Seamless integration with AI agents for intelligent conversations
- **Context Management**: Advanced context switching with persistent user preferences
  - Brand, time period, and metric filtering
  - Smart query suggestions based on active context
  - Multi-context comparative analysis support
- **Secure Authentication**: Google OAuth integration with NextAuth.js
- **Modern UI**: Responsive design with dark/light theme support
- **Production Ready**: Optimized build with TypeScript and ESLint

## 🛠️ Tech Stack

- **Framework**: Next.js 15.5.2 with Turbopack
- **Language**: TypeScript
- **Real-time Communication**: Native WebSocket with automatic reconnection
- **Styling**: Tailwind CSS 4 + shadcn/ui
- **UI Components**: shadcn/ui with Radix UI primitives
- **Authentication**: NextAuth.js with Google Provider
- **Database**: PostgreSQL with Prisma ORM
- **Icons**: Lucide React
- **Validation**: Zod

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fin_chat
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/fin_chat"

   # NextAuth
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key"

   # Google OAuth
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"

   # WebSocket Configuration
   NEXT_PUBLIC_WEBSOCKET_URL="ws://localhost:8880"
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🏗️ Production Build

```bash
npm run build
npm start
```

## 📁 Project Structure

```
fin_chat/
├── app/                    # Next.js app directory
│   ├── auth/              # Authentication pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   ├── Chat.tsx          # Main chat interface with WebSocket
│   ├── ConnectionStatus.tsx # WebSocket connection monitoring
│   ├── Context*.tsx      # Context management components
│   └── ...               # Other UI components
├── lib/                   # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── context.ts        # Context management service
│   ├── websocket-client.ts # WebSocket client with reconnection
│   ├── use-websocket-chat.ts # Custom WebSocket chat hook
│   └── schemas.ts        # Zod validation schemas
├── prisma/               # Database schema
└── types/                # TypeScript type definitions
```

## 🎨 UI Components

The application uses **shadcn/ui** for consistent, accessible, and customizable UI components:

- **Design System**: Built on Radix UI primitives with Tailwind CSS
- **Accessibility**: WCAG compliant components with proper ARIA attributes
- **Theming**: Consistent light/dark theme support with CSS variables
- **Type Safety**: Full TypeScript support with proper component typing
- **Testing**: Comprehensive test coverage for all UI components

### Available Components

- **Button**: Various variants (default, destructive, outline, ghost, link)
- **Badge**: Status indicators with custom variants
- **Card**: Content containers with header, content, and footer sections
- **Dialog**: Modal dialogs for forms and confirmations
- **Input/Textarea**: Form inputs with validation states
- **Dropdown Menu**: Accessible dropdown menus and selects
- **Avatar**: User profile images with fallbacks
- **Separator**: Visual dividers for content sections

## 🎯 Context Management

The application features advanced context management that allows users to:

- **Set Context**: Select brands, time periods, and metrics
- **Persistent Storage**: Context preferences are saved locally
- **Smart Suggestions**: Get AI-generated query suggestions based on active context
- **Comparative Analysis**: Ask questions like "Compare Brand A in July with Brand B in August"

## 🔌 WebSocket Configuration

The application uses WebSocket for real-time communication with fin_gateway:

- **Connection URL**: Configured via `NEXT_PUBLIC_WEBSOCKET_URL` environment variable
- **Default**: `ws://localhost:8880` (fin_gateway default port)
- **Auto-reconnection**: Exponential backoff strategy with max 30-second intervals
- **Connection Status**: Real-time UI indicators show connection state
- **Message Format**: Maintains full compatibility with existing fin_gateway payload structure
- **Offline Support**: Messages are queued when disconnected and sent upon reconnection

## 🔒 Authentication

Secure authentication is provided through:
- Google OAuth integration
- Session management with NextAuth.js
- Protected routes and API endpoints
- User role and company association support

## 🚀 Deployment

The application is ready for deployment on platforms like:
- Vercel (recommended)
- Netlify
- Railway
- Any Node.js hosting platform

Make sure to configure environment variables in your deployment platform.

## 📄 License

This project is private and proprietary.
