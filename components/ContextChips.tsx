"use client";

import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ContextItem, ChatContext } from "@/lib/schemas";
import { cn } from "@/lib/utils";

// Define chip variant types for better type safety
type ChipVariant = 'brand' | 'timePeriod' | 'metric';

interface ContextChipsProps {
  context: ChatContext;
  onRemoveItem: (type: keyof ChatContext, itemId: string) => void;
  className?: string;
}

interface ChipProps {
  item: ContextItem;
  type: keyof ChatContext;
  onRemove: (type: keyof ChatContext, itemId: string) => void;
  variant: ChipVariant;
}

function ContextChip({ item, type, onRemove, variant }: ChipProps) {
  const getVariantClasses = (variant: ChipVariant): string => {
    switch (variant) {
      case 'brand':
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 border-blue-200 dark:border-blue-800";
      case 'timePeriod':
        return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 border-green-200 dark:border-green-800";
      case 'metric':
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300 border-purple-200 dark:border-purple-800";
      default:
        return "";
    }
  };

  return (
    <Badge
      variant="outline"
      className={cn(
        "inline-flex items-center gap-1 rounded-full transition-colors duration-200 context-chip-enter",
        getVariantClasses(variant)
      )}
    >
      <span className="truncate max-w-24">{item.label}</span>
      <Button
        variant="ghost"
        size="icon"
        onClick={() => onRemove(type, item.id)}
        className="h-4 w-4 hover:bg-black/10 dark:hover:bg-white/10 rounded-full"
        aria-label={`Remove ${item.label}`}
      >
        <X size={10} />
      </Button>
    </Badge>
  );
}

export default function ContextChips({ context, onRemoveItem, className = "" }: ContextChipsProps) {
  const hasAnyContext = context.brands.length > 0 || context.timePeriods.length > 0 || context.metrics.length > 0;

  if (!hasAnyContext) {
    return null;
  }

  return (
    <div className={`flex flex-wrap gap-1.5 ${className}`}>
      {/* Brand chips */}
      {context.brands.map((brand) => (
        <ContextChip
          key={brand.id}
          item={brand}
          type="brands"
          onRemove={onRemoveItem}
          variant="brand"
        />
      ))}

      {/* Time period chips */}
      {context.timePeriods.map((period) => (
        <ContextChip
          key={period.id}
          item={period}
          type="timePeriods"
          onRemove={onRemoveItem}
          variant="timePeriod"
        />
      ))}

      {/* Metric chips */}
      {context.metrics.map((metric) => (
        <ContextChip
          key={metric.id}
          item={metric}
          type="metrics"
          onRemove={onRemoveItem}
          variant="metric"
        />
      ))}
    </div>
  );
}
