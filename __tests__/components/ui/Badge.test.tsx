import { render, screen } from '@testing-library/react'
import { Badge } from '@/components/ui/badge'

describe('Badge Component', () => {
  it('renders with default props', () => {
    render(<Badge>Default Badge</Badge>)
    const badge = screen.getByText('Default Badge')
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass('bg-primary', 'text-primary-foreground')
  })

  it('renders with different variants', () => {
    const { rerender } = render(<Badge variant="secondary">Secondary</Badge>)
    let badge = screen.getByText('Secondary')
    expect(badge).toHaveClass('bg-secondary', 'text-secondary-foreground')

    rerender(<Badge variant="destructive">Destructive</Badge>)
    badge = screen.getByText('Destructive')
    expect(badge).toHaveClass('bg-destructive', 'text-white')

    rerender(<Badge variant="outline">Outline</Badge>)
    badge = screen.getByText('Outline')
    expect(badge).toHaveClass('text-foreground')
  })

  it('applies custom className', () => {
    render(<Badge className="custom-badge">Custom</Badge>)
    const badge = screen.getByText('Custom')
    expect(badge).toHaveClass('custom-badge')
  })

  it('renders as child component when asChild is true', () => {
    render(
      <Badge asChild>
        <a href="/badge">Badge Link</a>
      </Badge>
    )
    
    const link = screen.getByRole('link')
    expect(link).toBeInTheDocument()
    expect(link).toHaveAttribute('href', '/badge')
    expect(link).toHaveClass('bg-primary', 'text-primary-foreground')
  })

  it('supports all HTML span attributes', () => {
    render(
      <Badge 
        title="Badge title"
        aria-label="Status badge"
        data-testid="status-badge"
      >
        Status
      </Badge>
    )
    
    const badge = screen.getByText('Status')
    expect(badge).toHaveAttribute('title', 'Badge title')
    expect(badge).toHaveAttribute('aria-label', 'Status badge')
    expect(badge).toHaveAttribute('data-testid', 'status-badge')
  })

  it('has correct base classes', () => {
    render(<Badge>Test</Badge>)
    const badge = screen.getByText('Test')
    
    expect(badge).toHaveClass(
      'inline-flex',
      'items-center',
      'justify-center',
      'rounded-md',
      'border',
      'px-2',
      'py-0.5',
      'text-xs',
      'font-medium'
    )
  })

  it('renders with icons', () => {
    render(
      <Badge>
        <span data-testid="icon">🔥</span>
        Hot Badge
      </Badge>
    )
    
    const badge = screen.getByText('Hot Badge')
    const icon = screen.getByTestId('icon')
    
    expect(badge).toBeInTheDocument()
    expect(icon).toBeInTheDocument()
  })
})
