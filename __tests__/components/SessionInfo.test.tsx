import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import SessionInfo from '@/components/SessionInfo'

// Mock the session module
jest.mock('@/lib/session', () => ({
  getOrCreateSession: jest.fn(),
  resetSession: jest.fn(),
}))





const mockSession = {
  userId: 'user-123',
  sessionId: 'session-456',
  createdAt: Date.now(),
}

describe('SessionInfo Component', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Set up the mocks
    const { getOrCreateSession, resetSession } = require('@/lib/session')
    getOrCreateSession.mockResolvedValue(mockSession)
    resetSession.mockResolvedValue({
      userId: 'user-123',
      sessionId: 'new-session-789',
      createdAt: Date.now(),
    })
  })

  it('renders session info component', async () => {
    render(<SessionInfo />)

    await waitFor(() => {
      expect(screen.getByText('Session Info')).toBeInTheDocument()
    })
  })

  it('expands and shows session details when clicked', async () => {
    render(<SessionInfo />)

    await waitFor(() => {
      const button = screen.getByRole('button', { name: /session info/i })
      fireEvent.click(button)
    })

    await waitFor(() => {
      expect(screen.getByText('User ID:')).toBeInTheDocument()
      expect(screen.getByText('Session ID:')).toBeInTheDocument()
    })
  })

  it('has proper card styling', async () => {
    render(<SessionInfo />)

    await waitFor(() => {
      const card = screen.getByText('Session Info').closest('[data-slot="card"]')
      expect(card).toBeInTheDocument()
      expect(card).toHaveClass('bg-card', 'text-card-foreground')
    })
  })

  it('renders reset session button when expanded', async () => {
    render(<SessionInfo />)

    await waitFor(() => {
      const button = screen.getByRole('button', { name: /session info/i })
      fireEvent.click(button)
    })

    await waitFor(() => {
      const resetButton = screen.getByRole('button', { name: /reset session/i })
      expect(resetButton).toBeInTheDocument()
    })
  })
})
