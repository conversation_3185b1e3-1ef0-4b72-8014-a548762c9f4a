import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useTheme } from 'next-themes'
import ThemeToggle from '@/components/ThemeToggle'

// Mock next-themes
jest.mock('next-themes')
const mockUseTheme = useTheme as jest.MockedFunction<typeof useTheme>

describe('ThemeToggle Component', () => {
  const mockSetTheme = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
    mockUseTheme.mockReturnValue({
      theme: 'light',
      setTheme: mockSetTheme,
      resolvedTheme: 'light',
      themes: ['light', 'dark'],
      systemTheme: 'light',
    })
  })

  it('renders theme toggle button', async () => {
    render(<ThemeToggle />)

    await waitFor(() => {
      const button = screen.getByRole('button')
      expect(button).toBeInTheDocument()
      expect(button).toHaveAttribute('aria-label', 'Toggle theme')
    })
  })

  it('renders moon icon in light theme after mounting', async () => {
    const { rerender } = render(<ThemeToggle />)
    
    // Simulate mounting
    rerender(<ThemeToggle />)
    
    await waitFor(() => {
      const button = screen.queryByRole('button')
      if (button) {
        expect(button).toBeInTheDocument()
        expect(button).toHaveAttribute('aria-label', 'Toggle theme')
      }
    })
  })

  it('renders sun icon in dark theme', async () => {
    mockUseTheme.mockReturnValue({
      theme: 'dark',
      setTheme: mockSetTheme,
      resolvedTheme: 'dark',
      themes: ['light', 'dark'],
      systemTheme: 'dark',
    })

    const { rerender } = render(<ThemeToggle />)
    
    // Simulate mounting
    rerender(<ThemeToggle />)
    
    await waitFor(() => {
      const button = screen.queryByRole('button')
      expect(button).toBeInTheDocument()
    })
  })

  it('toggles theme from light to dark when clicked', async () => {
    const { rerender } = render(<ThemeToggle />)
    
    // Simulate mounting
    rerender(<ThemeToggle />)
    
    await waitFor(() => {
      const button = screen.queryByRole('button')
      if (button) {
        fireEvent.click(button)
        expect(mockSetTheme).toHaveBeenCalledWith('dark')
      }
    })
  })

  it('toggles theme from dark to light when clicked', async () => {
    mockUseTheme.mockReturnValue({
      theme: 'dark',
      setTheme: mockSetTheme,
      resolvedTheme: 'dark',
      themes: ['light', 'dark'],
      systemTheme: 'dark',
    })

    const { rerender } = render(<ThemeToggle />)
    
    // Simulate mounting
    rerender(<ThemeToggle />)
    
    await waitFor(() => {
      const button = screen.queryByRole('button')
      if (button) {
        fireEvent.click(button)
        expect(mockSetTheme).toHaveBeenCalledWith('light')
      }
    })
  })

  it('has correct button styling', async () => {
    const { rerender } = render(<ThemeToggle />)
    
    // Simulate mounting
    rerender(<ThemeToggle />)
    
    await waitFor(() => {
      const button = screen.queryByRole('button')
      if (button) {
        expect(button).toHaveClass('h-6', 'w-6')
      }
    })
  })

  it('has proper accessibility attributes', async () => {
    const { rerender } = render(<ThemeToggle />)
    
    // Simulate mounting
    rerender(<ThemeToggle />)
    
    await waitFor(() => {
      const button = screen.queryByRole('button')
      if (button) {
        expect(button).toHaveAttribute('aria-label', 'Toggle theme')
      }
    })
  })
})
