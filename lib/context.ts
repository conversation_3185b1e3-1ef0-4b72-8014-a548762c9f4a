'use client';

import { Chat<PERSON>ontext, ChatContextSchema, ContextItem, PREDEFINED_BRANDS, PREDEFINED_TIME_PERIODS, PREDEFINED_METRICS } from './schemas';

const CONTEXT_STORAGE_KEY = 'fin_chat_context';

export interface ContextService {
  getContext(): ChatContext;
  updateContext(context: Partial<ChatContext>): ChatContext;
  addContextItem(type: keyof ChatContext, item: ContextItem): ChatContext;
  removeContextItem(type: keyof ChatContext, itemId: string): ChatContext;
  clearContext(): ChatContext;
  getAvailableOptions(type: keyof ChatContext): ContextItem[];
  formatContextForQuery(context: ChatContext): string;
}

class ContextManager implements ContextService {
  private getDefaultContext(): ChatContext {
    return {
      brands: [],
      timePeriods: [],
      metrics: [],
      lastUpdated: Date.now(),
    };
  }

  private saveContext(context: ChatContext): void {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(CONTEXT_STORAGE_KEY, JSON.stringify(context));
      } catch (error) {
        console.warn('Failed to save context to localStorage:', error);
      }
    }
  }

  getContext(): ChatContext {
    if (typeof window === 'undefined') {
      return this.getDefaultContext();
    }

    try {
      const stored = localStorage.getItem(CONTEXT_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        const validated = ChatContextSchema.parse(parsed);
        return validated;
      }
    } catch (error) {
      console.warn('Failed to load context from localStorage:', error);
    }

    return this.getDefaultContext();
  }

  updateContext(updates: Partial<ChatContext>): ChatContext {
    const currentContext = this.getContext();
    const updatedContext: ChatContext = {
      ...currentContext,
      ...updates,
      lastUpdated: Date.now(),
    };

    this.saveContext(updatedContext);
    return updatedContext;
  }

  addContextItem(type: keyof ChatContext, item: ContextItem): ChatContext {
    const currentContext = this.getContext();
    
    // Skip non-array fields
    if (type === 'lastUpdated') {
      return currentContext;
    }

    const currentItems = currentContext[type] as ContextItem[];
    
    // Check if item already exists
    const existingIndex = currentItems.findIndex(existing => existing.id === item.id);
    
    let updatedItems: ContextItem[];
    if (existingIndex >= 0) {
      // Update existing item
      updatedItems = [...currentItems];
      updatedItems[existingIndex] = item;
    } else {
      // Add new item
      updatedItems = [...currentItems, item];
    }

    return this.updateContext({
      [type]: updatedItems,
    });
  }

  removeContextItem(type: keyof ChatContext, itemId: string): ChatContext {
    const currentContext = this.getContext();
    
    // Skip non-array fields
    if (type === 'lastUpdated') {
      return currentContext;
    }

    const currentItems = currentContext[type] as ContextItem[];
    const filteredItems = currentItems.filter(item => item.id !== itemId);

    return this.updateContext({
      [type]: filteredItems,
    });
  }

  clearContext(): ChatContext {
    const clearedContext = this.getDefaultContext();
    this.saveContext(clearedContext);
    return clearedContext;
  }

  getAvailableOptions(type: keyof ChatContext): ContextItem[] {
    switch (type) {
      case 'brands':
        return PREDEFINED_BRANDS;
      case 'timePeriods':
        return PREDEFINED_TIME_PERIODS;
      case 'metrics':
        return PREDEFINED_METRICS;
      default:
        return [];
    }
  }

  formatContextForQuery(context: ChatContext): string {
    const parts: string[] = [];

    if (context.brands.length > 0) {
      const brandLabels = context.brands.map(b => b.label).join(', ');
      parts.push(`Brands: ${brandLabels}`);
    }

    if (context.timePeriods.length > 0) {
      const periodLabels = context.timePeriods.map(p => p.label).join(', ');
      parts.push(`Time Periods: ${periodLabels}`);
    }

    if (context.metrics.length > 0) {
      const metricLabels = context.metrics.map(m => m.label).join(', ');
      parts.push(`Metrics: ${metricLabels}`);
    }

    return parts.length > 0 ? `Context: ${parts.join(' | ')}` : '';
  }
}

// Export singleton instance
export const contextService = new ContextManager();

// Export utility functions
export function getContextSummary(context: ChatContext): string {
  const totalItems = context.brands.length + context.timePeriods.length + context.metrics.length;
  if (totalItems === 0) return 'No context set';
  
  const parts: string[] = [];
  if (context.brands.length > 0) parts.push(`${context.brands.length} brand${context.brands.length > 1 ? 's' : ''}`);
  if (context.timePeriods.length > 0) parts.push(`${context.timePeriods.length} period${context.timePeriods.length > 1 ? 's' : ''}`);
  if (context.metrics.length > 0) parts.push(`${context.metrics.length} metric${context.metrics.length > 1 ? 's' : ''}`);
  
  return parts.join(', ');
}

export function hasActiveContext(context: ChatContext): boolean {
  return context.brands.length > 0 || context.timePeriods.length > 0 || context.metrics.length > 0;
}
