# Finity AI Chat

A modern, production-ready AI chat application built with Next.js, featuring advanced context management and secure authentication.

## 🚀 Features

- **AI-Powered Chat**: Seamless integration with AI agents for intelligent conversations
- **Context Management**: Advanced context switching with persistent user preferences
  - Brand, time period, and metric filtering
  - Smart query suggestions based on active context
  - Multi-context comparative analysis support
- **Secure Authentication**: Google OAuth integration with NextAuth.js
- **Modern UI**: Responsive design with dark/light theme support
- **Real-time Chat**: Streaming responses with auto-scroll functionality
- **Production Ready**: Optimized build with TypeScript and ESLint

## 🛠️ Tech Stack

- **Framework**: Next.js 15.5.2 with Turbopack
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4 + shadcn/ui
- **UI Components**: shadcn/ui with Radix UI primitives
- **Authentication**: NextAuth.js with Google Provider
- **Database**: PostgreSQL with Prisma ORM
- **AI Integration**: Vercel AI SDK
- **Icons**: Lucide React
- **Validation**: Zod
- **Testing**: Jest + React Testing Library

## 📦 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd fin_chat
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Configure the following variables:
   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/fin_chat"

   # NextAuth
   NEXTAUTH_URL="http://localhost:3000"
   NEXTAUTH_SECRET="your-secret-key"

   # Google OAuth
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"

   # AI Agent (optional)
   AI_AGENT_URL="https://your-ai-agent-endpoint"
   ```

4. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

   Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage
```

## 🏗️ Production Build

```bash
npm run build
npm start
```

## 📁 Project Structure

```
fin_chat/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   ├── Chat.tsx          # Main chat interface
│   ├── Context*.tsx      # Context management components
│   └── ...               # Other UI components
├── lib/                   # Utility libraries
│   ├── auth.ts           # NextAuth configuration
│   ├── context.ts        # Context management service
│   └── schemas.ts        # Zod validation schemas
├── prisma/               # Database schema
└── types/                # TypeScript type definitions
```

## 🎨 UI Components

The application uses **shadcn/ui** for consistent, accessible, and customizable UI components:

- **Design System**: Built on Radix UI primitives with Tailwind CSS
- **Accessibility**: WCAG compliant components with proper ARIA attributes
- **Theming**: Consistent light/dark theme support with CSS variables
- **Type Safety**: Full TypeScript support with proper component typing
- **Testing**: Comprehensive test coverage for all UI components

### Available Components

- **Button**: Various variants (default, destructive, outline, ghost, link)
- **Badge**: Status indicators with custom variants
- **Card**: Content containers with header, content, and footer sections
- **Dialog**: Modal dialogs for forms and confirmations
- **Input/Textarea**: Form inputs with validation states
- **Dropdown Menu**: Accessible dropdown menus and selects
- **Avatar**: User profile images with fallbacks
- **Separator**: Visual dividers for content sections

## 🎯 Context Management

The application features advanced context management that allows users to:

- **Set Context**: Select brands, time periods, and metrics
- **Persistent Storage**: Context preferences are saved locally
- **Smart Suggestions**: Get AI-generated query suggestions based on active context
- **Comparative Analysis**: Ask questions like "Compare Brand A in July with Brand B in August"

## 🔒 Authentication

Secure authentication is provided through:
- Google OAuth integration
- Session management with NextAuth.js
- Protected routes and API endpoints
- User role and company association support

## 🚀 Deployment

The application is ready for deployment on platforms like:
- Vercel (recommended)
- Netlify
- Railway
- Any Node.js hosting platform

Make sure to configure environment variables in your deployment platform.

## 📄 License

This project is private and proprietary.
