"use client";

import { useState } from "react";
import {
  MessageSquare,
  <PERSON>ting<PERSON>,
  User,
  MoreHorizontal,
  Search,
  Archive,
  Trash2,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import ThemeToggle from "./ThemeToggle";
import SessionInfo from "./SessionInfo";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ChatHistoryItem {
  id: string;
  title: string;
  isActive: boolean;
  timestamp: string;
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const chatHistory: ChatHistoryItem[] = [];

  const filteredChats = chatHistory.filter((chat) =>
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed inset-y-0 left-0 z-50 w-72 h-full flex flex-col border-r bg-background border-border
          transition-transform duration-300 ease-in-out lg:relative lg:z-auto
          ${isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}`}
      >
        {/* Header */}
        <div className="p-3 border-b border-border">
          <div className="relative">
            <Search
              size={14}
              className="absolute left-2.5 top-1/2 -translate-y-1/2 text-muted-foreground"
            />
            <Input
              type="text"
              placeholder="Search conversations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 h-8 text-xs"
            />
          </div>
        </div>

        {/* Session Info */}
        <div className="p-3">
          <SessionInfo />
        </div>

        {/* Chat History */}
        <div className="flex-1 overflow-y-auto p-3">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400">
              Recent chats
            </h3>
            <div className="flex gap-1">
              <button
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Archive"
              >
                <Archive size={12} className="text-gray-400" />
              </button>
              <button
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Delete"
              >
                <Trash2 size={12} className="text-gray-400" />
              </button>
            </div>
          </div>

          <div className="space-y-0.5">
            {filteredChats.length > 0 ? (
              filteredChats.map((chat) => (
                <div
                  key={chat.id}
                  className={`group flex items-center justify-between p-2 rounded-md cursor-pointer transition-colors ${chat.isActive
                    ? "bg-primary/10 border border-primary/20"
                    : "hover:bg-accent"
                    }`}
                >
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div
                      className={`w-1.5 h-1.5 rounded-full ${chat.isActive
                        ? "bg-primary"
                        : "bg-muted-foreground"
                        }`}
                    />
                    <div className="min-w-0 flex-1">
                      <div
                        className={`truncate text-xs ${chat.isActive
                          ? "font-medium text-primary"
                          : "text-foreground"
                          }`}
                      >
                        {chat.title}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {chat.timestamp}
                      </div>
                    </div>
                  </div>
                  <button className="opacity-0 group-hover:opacity-100 p-0.5 rounded hover:bg-accent transition">
                    <MoreHorizontal size={12} className="text-muted-foreground" />
                  </button>
                </div>
              ))
            ) : (
              <div className="py-6 text-center">
                <MessageSquare
                  size={24}
                  className="mx-auto mb-2 text-gray-300 dark:text-gray-600"
                />
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  No conversations found
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="h-16 p-3 border-t border-border">
          <div className="flex items-center justify-between h-full">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 flex items-center justify-center rounded-full bg-primary">
                <User size={14} className="text-primary-foreground" />
              </div>
              <div>
                <div className="truncate text-xs font-medium text-foreground">
                  Guest
                </div>
                <div className="truncate text-xs text-muted-foreground">
                  Free plan
                </div>
              </div>
            </div>
            <div className="flex gap-1">
              <ThemeToggle />
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <Settings size={14} className="text-muted-foreground" />
              </Button>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}
