'use client';

import { useState, useEffect } from 'react';
import { User, RefreshCw, Info } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { getOrCreateSession, resetSession, type SessionData } from '@/lib/session';

export default function SessionInfo() {
  const [session, setSession] = useState<SessionData | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    setSession(getOrCreateSession());
  }, []);

  const handleResetSession = () => {
    const newSession = resetSession();
    setSession(newSession);
    window.location.reload();
  };

  if (!session) return null;

  return (
    <Card className="py-0">
      <CardContent className="p-0">
        <Button
          variant="ghost"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full p-3 flex items-center justify-between text-left h-auto rounded-lg"
        >
          <div className="flex items-center gap-2">
            <Info size={16} className="text-muted-foreground" />
            <span className="text-sm font-medium">
              Session Info
            </span>
          </div>
          <div className={`transform transition-transform ${isExpanded ? 'rotate-180' : ''}`}>
            <svg width="12" height="12" viewBox="0 0 12 12" className="text-muted-foreground">
              <path d="M2 4l4 4 4-4" stroke="currentColor" strokeWidth="1.5" fill="none" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
        </Button>

        {isExpanded && (
          <div className="px-3 pb-3 space-y-3 border-t border-border pt-3">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-xs">
                <User size={12} className="text-muted-foreground" />
                <span className="text-muted-foreground">User ID:</span>
                <code className="bg-muted px-1 py-0.5 rounded text-foreground">
                  {session.userId}
                </code>
              </div>

              <div className="flex items-center gap-2 text-xs">
                <RefreshCw size={12} className="text-muted-foreground" />
                <span className="text-muted-foreground">Session ID:</span>
                <code className="bg-muted px-1 py-0.5 rounded text-foreground break-all">
                  {session.sessionId}
                </code>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleResetSession}
              className="w-full text-xs border-primary/20 text-primary hover:bg-primary/10"
            >
              Reset Session
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
