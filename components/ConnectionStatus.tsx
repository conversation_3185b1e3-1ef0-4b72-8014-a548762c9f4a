'use client';

import { useState, useEffect } from 'react';
import { Wifi, WifiOff, RotateCcw, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { webSocketClient, ConnectionStatus as Status } from '@/lib/websocket-client';

interface ConnectionStatusProps {
  className?: string;
}

export default function ConnectionStatus({ className = '' }: ConnectionStatusProps) {
  const [status, setStatus] = useState<Status>('disconnected');
  const [showDetails, setShowDetails] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  useEffect(() => {
    const unsubscribe = webSocketClient.onStatusChange((newStatus) => {
      const prevStatus = status;
      setStatus(newStatus);

      // Show toast notifications for status changes
      if (prevStatus !== newStatus) {
        let message = '';
        switch (newStatus) {
          case 'connected':
            message = 'Connected to server';
            break;
          case 'disconnected':
            message = 'Disconnected from server';
            break;
          case 'error':
            message = 'Connection failed';
            break;
        }

        if (message && prevStatus !== 'disconnected') { // Don't show toast on initial load
          setToastMessage(message);
          setShowToast(true);
          setTimeout(() => setShowToast(false), 3000);
        }
      }
    });

    setStatus(webSocketClient.getConnectionStatus());
    return unsubscribe;
  }, [status]);

  const handleRetry = () => {
    webSocketClient.connect().catch(console.error);
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <Wifi size={16} className="text-green-500" />;
      case 'connecting':
        return <RotateCcw size={16} className="text-yellow-500 animate-spin" />;
      case 'disconnected':
        return <WifiOff size={16} className="text-gray-500" />;
      case 'error':
        return <AlertCircle size={16} className="text-red-500" />;
      default:
        return <WifiOff size={16} className="text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'disconnected':
        return 'Disconnected';
      case 'error':
        return 'Connection Error';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'text-green-600 dark:text-green-400';
      case 'connecting':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'disconnected':
        return 'text-gray-600 dark:text-gray-400';
      case 'error':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  return (
    <>
      <div className={`flex items-center gap-2 ${className}`}>
        <div
          className="flex items-center gap-2 cursor-pointer"
          onClick={() => setShowDetails(!showDetails)}
        >
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>

        {(status === 'error' || status === 'disconnected') && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRetry}
            className="h-6 px-2 text-xs"
          >
            <RotateCcw size={12} className="mr-1" />
            Retry
          </Button>
        )}

      {showDetails && (
        <div className="absolute top-full left-0 mt-2 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[200px]">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">WebSocket Status</span>
              <div className="flex items-center gap-1">
                {getStatusIcon()}
                <span className={`text-xs ${getStatusColor()}`}>
                  {getStatusText()}
                </span>
              </div>
            </div>
            
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {status === 'connected' && 'Real-time communication active'}
              {status === 'connecting' && 'Establishing connection...'}
              {status === 'disconnected' && 'No active connection'}
              {status === 'error' && 'Failed to connect to server'}
            </div>

            {(status === 'error' || status === 'disconnected') && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="w-full h-7 text-xs"
              >
                <RotateCcw size={12} className="mr-1" />
                Reconnect
              </Button>
            )}
          </div>
        </div>
      )}
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className="fixed top-4 right-4 z-50 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 flex items-center gap-2 animate-in slide-in-from-right-5">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {toastMessage}
          </span>
        </div>
      )}
    </>
  );
}
