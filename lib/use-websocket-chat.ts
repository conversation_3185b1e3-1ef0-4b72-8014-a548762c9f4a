'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { webSocketClient, ConnectionStatus } from './websocket-client';
import { Message, MessagePart } from './schemas';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  id?: string;
}

export interface UseWebSocketChatOptions {
  body?: {
    userId?: string;
    sessionId?: string;
    userEmail?: string;
  };
}

export interface UseWebSocketChatReturn {
  messages: ChatMessage[];
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  isLoading: boolean;
  connectionStatus: ConnectionStatus;
  error: string | null;
  retry: () => void;
}

export function useWebSocketChat(_options: UseWebSocketChatOptions = {}): UseWebSocketChatReturn {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('disconnected');
  const [error, setError] = useState<string | null>(null);

  const abortControllerRef = useRef<AbortController | null>(null);
  // Note: body options are available for future use (userId, sessionId, userEmail)
  // Currently WebSocket sends full message history, but these could be used for user context

  // Initialize WebSocket connection
  useEffect(() => {
    const unsubscribeStatus = webSocketClient.onStatusChange((status) => {
      setConnectionStatus(status);
      if (status === 'connected') {
        setError(null);
      } else if (status === 'error') {
        setError('Connection failed. Please check your network and try again.');
      }
    });

    // Connect on mount
    webSocketClient.connect().catch((err) => {
      setError(err.message);
    });

    return () => {
      unsubscribeStatus();
    };
  }, []);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInput(e.target.value);
  }, []);

  const convertToWebSocketMessage = useCallback((chatMessage: ChatMessage): Message => {
    const parts: MessagePart[] = [
      {
        type: 'text',
        text: chatMessage.content,
      }
    ];

    return {
      role: chatMessage.role,
      content: chatMessage.content,
      parts,
    };
  }, []);

  const sendMessage = useCallback(async (userMessage: string) => {
    if (!userMessage.trim() || isLoading) return;

    setIsLoading(true);
    setError(null);

    // Create user message
    const userChatMessage: ChatMessage = {
      role: 'user',
      content: userMessage.trim(),
      id: `user_${Date.now()}`,
    };

    // Add user message to chat
    setMessages(prev => [...prev, userChatMessage]);

    try {
      // Prepare all messages for WebSocket (including conversation history)
      const allMessages = [...messages, userChatMessage];
      const webSocketMessages = allMessages.map(convertToWebSocketMessage);

      // Create abort controller for this request
      abortControllerRef.current = new AbortController();

      // Send to WebSocket
      const response = await webSocketClient.sendMessage(webSocketMessages);

      // Handle response
      if (response.type === 'chat_response' && typeof response.payload === 'string') {
        const assistantMessage: ChatMessage = {
          role: 'assistant',
          content: response.payload,
          id: `assistant_${Date.now()}`,
        };

        setMessages(prev => [...prev, assistantMessage]);
      } else if (response.type === 'error') {
        const errorMessage = typeof response.payload === 'object'
          ? response.payload.error
          : 'An error occurred while processing your message.';
        setError(errorMessage);
      }

    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message || 'Failed to send message. Please try again.');
      }
    } finally {
      setIsLoading(false);
      abortControllerRef.current = null;
    }
  }, [messages, isLoading, convertToWebSocketMessage]);

  const handleSubmit = useCallback((e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!input.trim()) return;

    const messageToSend = input;
    setInput(''); // Clear input immediately
    sendMessage(messageToSend);
  }, [input, sendMessage]);

  const retry = useCallback(() => {
    setError(null);
    webSocketClient.connect().catch((err) => {
      setError(err.message);
    });
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    connectionStatus,
    error,
    retry,
  };
}
